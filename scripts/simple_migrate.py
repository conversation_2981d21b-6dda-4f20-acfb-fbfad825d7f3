#!/usr/bin/env python3
"""
简化的数据迁移脚本
"""

import os
import sys
import sqlite3
import logging

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.config_manager import config_manager
from app.services.postgresql_adapter import PostgreSQLAdapter

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def main():
    try:
        # 1. 获取配置
        db_config = config_manager.get_database_config()
        print(f"数据库配置: {db_config.get_safe_connection_string()}")
        
        # 2. 创建PostgreSQL适配器
        pg_adapter = PostgreSQLAdapter(db_config)
        
        # 3. 连接并设置PostgreSQL
        pg_adapter.connect()
        pg_adapter.create_tables()
        pg_adapter.create_indexes()
        print("PostgreSQL表结构创建完成")
        
        # 4. 从SQLite导出数据
        project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        sqlite_path = os.path.join(project_root, 'serial_numbers.db')
        
        conn = sqlite3.connect(sqlite_path)
        conn.row_factory = sqlite3.Row
        cursor = conn.cursor()
        
        cursor.execute("""
            SELECT id, serial_number, created_at, created_by, notes
            FROM serial_numbers
            ORDER BY id
        """)
        
        rows = cursor.fetchall()
        data = [
            {
                'id': row['id'],
                'serial_number': row['serial_number'],
                'created_at': row['created_at'],
                'created_by': row['created_by'],
                'notes': row['notes']
            }
            for row in rows
        ]
        
        conn.close()
        print(f"从SQLite导出了 {len(data)} 条记录")
        
        # 5. 导入到PostgreSQL
        if data:
            result = pg_adapter.import_data(data)
            print(f"导入结果: 成功 {result['imported_count']} 条，失败 {result['failed_count']} 条")
            if result['errors']:
                print(f"错误: {result['errors'][:5]}...")  # 只显示前5个错误
        
        # 6. 验证结果
        pg_count = pg_adapter.get_record_count()
        print(f"PostgreSQL中的记录数: {pg_count}")
        
        # 7. 获取统计信息
        stats = pg_adapter.get_statistics()
        print(f"统计信息: 总数 {stats['total_count']}, 今日 {stats['today_count']}, 本周 {stats['week_count']}")
        
        pg_adapter.disconnect()
        print("✅ 迁移完成！")
        
    except Exception as e:
        print(f"❌ 迁移失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == '__main__':
    main()