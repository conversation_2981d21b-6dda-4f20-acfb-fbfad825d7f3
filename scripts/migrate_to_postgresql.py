#!/usr/bin/env python3
"""
数据迁移工具
从SQLite数据库迁移串码数据到PostgreSQL数据库
"""

import os
import sys
import sqlite3
import shutil
import logging
from datetime import datetime
from typing import Dict, List, Any

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.services.config_manager import config_manager, DatabaseConfig
from app.services.postgresql_adapter import PostgreSQLAdapter
from app.services.database_adapter import DatabaseError

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('migration.log')
    ]
)
logger = logging.getLogger(__name__)


class MigrationTool:
    """数据迁移工具类"""
    
    def __init__(self):
        """初始化迁移工具"""
        self.sqlite_path = None
        self.postgresql_adapter = None
        self.backup_path = None
        
    def setup(self) -> bool:
        """设置迁移环境
        
        Returns:
            bool: 设置是否成功
        """
        try:
            # 获取配置
            db_config = config_manager.get_database_config()
            backup_config = config_manager.get_backup_config()
            
            # 设置SQLite路径
            project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
            self.sqlite_path = os.path.join(project_root, 'serial_numbers.db')
            self.backup_path = backup_config['sqlite_backup_path']
            
            # 检查SQLite文件是否存在
            if not os.path.exists(self.sqlite_path):
                logger.error(f"SQLite数据库文件不存在: {self.sqlite_path}")
                return False
            
            # 创建PostgreSQL适配器
            if db_config.database_type != 'postgresql':
                logger.error("当前配置不是PostgreSQL，请检查环境变量")
                return False
            
            self.postgresql_adapter = PostgreSQLAdapter(db_config)
            
            logger.info("迁移工具设置完成")
            return True
            
        except Exception as e:
            logger.error(f"设置迁移工具失败: {e}")
            return False
    
    def create_backup(self) -> bool:
        """创建SQLite数据库备份
        
        Returns:
            bool: 备份是否成功
        """
        try:
            if os.path.exists(self.backup_path):
                # 如果备份已存在，创建带时间戳的备份
                timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
                timestamped_backup = f"{self.backup_path}.{timestamp}"
                shutil.copy2(self.backup_path, timestamped_backup)
                logger.info(f"已存在的备份已保存为: {timestamped_backup}")
            
            shutil.copy2(self.sqlite_path, self.backup_path)
            logger.info(f"SQLite数据库备份已创建: {self.backup_path}")
            return True
            
        except Exception as e:
            logger.error(f"创建备份失败: {e}")
            return False
    
    def export_sqlite_data(self) -> List[Dict[str, Any]]:
        """从SQLite导出数据
        
        Returns:
            List[Dict]: 导出的数据列表
        """
        try:
            conn = sqlite3.connect(self.sqlite_path)
            conn.row_factory = sqlite3.Row
            cursor = conn.cursor()
            
            cursor.execute("""
                SELECT id, serial_number, created_at, created_by, notes
                FROM serial_numbers
                ORDER BY id
            """)
            
            rows = cursor.fetchall()
            data = [
                {
                    'id': row['id'],
                    'serial_number': row['serial_number'],
                    'created_at': row['created_at'],
                    'created_by': row['created_by'],
                    'notes': row['notes']
                }
                for row in rows
            ]
            
            conn.close()
            
            logger.info(f"从SQLite导出了 {len(data)} 条记录")
            return data
            
        except Exception as e:
            logger.error(f"导出SQLite数据失败: {e}")
            return []
    
    def setup_postgresql(self) -> bool:
        """设置PostgreSQL数据库
        
        Returns:
            bool: 设置是否成功
        """
        try:
            # 连接数据库
            if not self.postgresql_adapter.connect():
                logger.error("无法连接到PostgreSQL数据库")
                return False
            
            # 创建表结构
            self.postgresql_adapter.create_tables()
            
            # 创建索引
            self.postgresql_adapter.create_indexes()
            
            # 验证表结构
            if not self.postgresql_adapter.validate_schema():
                logger.error("PostgreSQL表结构验证失败")
                return False
            
            logger.info("PostgreSQL数据库设置完成")
            return True
            
        except DatabaseError as e:
            logger.error(f"设置PostgreSQL失败: {e}")
            return False
    
    def migrate_data(self, data: List[Dict[str, Any]]) -> Dict[str, Any]:
        """迁移数据到PostgreSQL
        
        Args:
            data: 要迁移的数据列表
            
        Returns:
            Dict: 迁移结果
        """
        try:
            if not data:
                logger.warning("没有数据需要迁移")
                return {
                    'success': True,
                    'imported_count': 0,
                    'failed_count': 0,
                    'errors': []
                }
            
            logger.info(f"开始迁移 {len(data)} 条记录...")
            
            # 使用适配器的批量导入功能
            result = self.postgresql_adapter.import_data(data)
            
            if result['success']:
                logger.info(f"数据迁移完成: 成功 {result['imported_count']} 条，失败 {result['failed_count']} 条")
                if result['errors']:
                    logger.warning(f"迁移过程中的错误: {result['errors']}")
            else:
                logger.error("数据迁移失败")
            
            return result
            
        except Exception as e:
            logger.error(f"迁移数据失败: {e}")
            return {
                'success': False,
                'imported_count': 0,
                'failed_count': len(data),
                'errors': [str(e)]
            }
    
    def validate_migration(self, original_count: int) -> bool:
        """验证迁移结果
        
        Args:
            original_count: 原始数据记录数
            
        Returns:
            bool: 验证是否通过
        """
        try:
            # 检查记录数
            pg_count = self.postgresql_adapter.get_record_count()
            
            if pg_count != original_count:
                logger.error(f"记录数不匹配: SQLite {original_count} vs PostgreSQL {pg_count}")
                return False
            
            # 检查统计信息
            stats = self.postgresql_adapter.get_statistics()
            if 'error' in stats:
                logger.error(f"获取统计信息失败: {stats['error']}")
                return False
            
            logger.info(f"迁移验证通过: 共 {pg_count} 条记录")
            logger.info(f"统计信息: 总数 {stats['total_count']}, 今日 {stats['today_count']}, 本周 {stats['week_count']}")
            
            return True
            
        except Exception as e:
            logger.error(f"验证迁移失败: {e}")
            return False
    
    def run_migration(self) -> bool:
        """执行完整的迁移流程
        
        Returns:
            bool: 迁移是否成功
        """
        logger.info("=" * 60)
        logger.info("开始数据库迁移流程")
        logger.info("=" * 60)
        
        try:
            # 1. 设置环境
            if not self.setup():
                logger.error("环境设置失败")
                return False
            
            # 2. 创建备份
            if not self.create_backup():
                logger.error("创建备份失败")
                return False
            
            # 3. 导出SQLite数据
            data = self.export_sqlite_data()
            if not data:
                logger.warning("没有数据需要迁移")
                # 即使没有数据，也要设置PostgreSQL
            
            original_count = len(data)
            
            # 4. 设置PostgreSQL
            if not self.setup_postgresql():
                logger.error("PostgreSQL设置失败")
                return False
            
            # 5. 迁移数据
            if data:
                result = self.migrate_data(data)
                if not result['success']:
                    logger.error("数据迁移失败")
                    return False
            
            # 6. 验证迁移
            if not self.validate_migration(original_count):
                logger.error("迁移验证失败")
                return False
            
            logger.info("=" * 60)
            logger.info("数据库迁移完成！")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"迁移过程中发生错误: {e}")
            return False
        
        finally:
            # 清理资源
            if self.postgresql_adapter:
                self.postgresql_adapter.disconnect()


def main():
    """主函数"""
    print("PostgreSQL数据迁移工具")
    print("=" * 40)
    
    # 确认迁移
    response = input("确认要将SQLite数据迁移到PostgreSQL吗？(y/N): ")
    if response.lower() != 'y':
        print("迁移已取消")
        return
    
    # 执行迁移
    migration_tool = MigrationTool()
    success = migration_tool.run_migration()
    
    if success:
        print("\n✅ 迁移成功完成！")
        print("\n后续步骤:")
        print("1. 更新.env文件，设置DATABASE_TYPE=postgresql")
        print("2. 重启应用服务")
        print("3. 验证应用功能正常")
        sys.exit(0)
    else:
        print("\n❌ 迁移失败！")
        print("\n回滚步骤:")
        print("1. 检查migration.log文件查看详细错误")
        print("2. 确保.env文件中DATABASE_TYPE=sqlite")
        print("3. 确认SQLite备份文件完整")
        sys.exit(1)


if __name__ == '__main__':
    main()