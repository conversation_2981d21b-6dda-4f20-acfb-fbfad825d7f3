"""
辅助函数模块，提供各种通用功能
"""
from flask import current_app
import datetime


def status_class_filter(status):
    """
    根据状态返回对应的CSS类名
    
    Args:
        status: 状态文本
    
    Returns:
        对应的CSS类名
    """
    status_mapping = {
        '逾期还款': 'status-overdue',
        '提前还款': 'status-early',
        '按时还款': 'status-ontime',
        '账单日': 'status-upcoming',
        '逾期未还': 'status-overdue',
        '催收': 'status-collection',
        '诉讼': 'status-litigation'
    }
    
    return status_mapping.get(status, '')


def format_date(date_str, format_in='%Y-%m-%d', format_out='%Y-%m-%d'):
    """
    格式化日期字符串
    
    Args:
        date_str: 日期字符串
        format_in: 输入日期格式
        format_out: 输出日期格式
    
    Returns:
        格式化后的日期字符串
    """
    try:
        if not date_str:
            return ''
        date_obj = datetime.datetime.strptime(date_str, format_in)
        return date_obj.strftime(format_out)
    except Exception:
        return date_str


def format_currency(amount):
    """
    格式化货币金额
    
    Args:
        amount: 金额数值
    
    Returns:
        格式化后的金额字符串
    """
    try:
        return f"¥{float(amount):,.2f}"
    except (ValueError, TypeError):
        return str(amount)


def static_file_with_version(filename):
    """
    为静态文件添加版本号，解决缓存问题
    
    Args:
        filename: 静态文件路径
    
    Returns:
        带版本号的静态文件URL
    """
    import os
    import time
    from flask import url_for, has_request_context
    
    try:
        # 获取文件的完整路径
        static_folder = current_app.static_folder
        file_path = os.path.join(static_folder, filename)
        
        # 如果文件存在，使用文件修改时间作为版本号
        if os.path.exists(file_path):
            mtime = os.path.getmtime(file_path)
            version = str(int(mtime))
        else:
            # 如果文件不存在，使用当前时间戳
            version = str(int(time.time()))
        
        # 检查是否在请求上下文中
        if has_request_context():
            # 在请求上下文中，可以安全使用url_for
            base_url = url_for('static', filename=filename)
            return f"{base_url}?v={version}"
        else:
            # 不在请求上下文中，手动构建URL
            return f"/static/{filename}?v={version}"
            
    except Exception:
        # 如果出错，返回基础静态URL
        if has_request_context():
            try:
                return url_for('static', filename=filename)
            except Exception:
                pass
        # 最终降级方案
        return f"/static/{filename}"


def register_template_filters(app):
    """
    注册模板过滤器
    
    Args:
        app: Flask应用实例
    """
    app.jinja_env.filters['status_class'] = status_class_filter
    app.jinja_env.filters['format_date'] = format_date
    app.jinja_env.filters['format_currency'] = format_currency
    
    # 注册静态文件版本控制函数为全局函数
    app.jinja_env.globals['static_versioned'] = static_file_with_version