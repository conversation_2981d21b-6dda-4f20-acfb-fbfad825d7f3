{% extends "base.html" %}

{% block title %}太享查询系统 - 首页{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- 引入侧边栏模板 -->
    {% include 'sidebar.html' %}

    <!-- 主内容区 -->
    <div class="main-content">
        <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
            <h1 class="h2">首页</h1>
            <div class="h6 text-muted">
                <span class="badge bg-primary">{{ version }}</span>
            </div>
        </div>

        <!-- 工具卡片区域 -->
        <div class="row g-4 mb-4">
            <!-- 系统状态卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-primary-light me-3">
                                <i class="bi bi-activity"></i>
                            </div>
                            <h5 class="card-title mb-0">系统状态</h5>
                        </div>
                        <h2 class="mb-2">
                            {% if stats.system_status == 'normal' %}
                                <span class="text-success">
                                    <i class="bi bi-check-circle"></i> 正常
                                </span>
                            {% else %}
                                <span class="text-warning">
                                    <i class="bi bi-exclamation-triangle"></i> 未知
                                </span>
                            {% endif %}
                        </h2>
                        <p class="card-text text-muted">
                            API状态:
                            {% if stats.api_status == 'online' %}
                                <span class="badge bg-success">在线</span>
                            {% else %}
                                <span class="badge bg-danger">离线</span>
                            {% endif %}
                        </p>
                    </div>
                </div>
            </div>

            <!-- 待处理订单卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-info-light me-3">
                                <i class="bi bi-calendar2-check"></i>
                            </div>
                            <h5 class="card-title mb-0">今日待处理</h5>
                        </div>
                        <h2 class="mb-2">{{ stats.today_orders }}</h2>
                        <p class="card-text text-muted">
                            <a href="javascript:void(0);" onclick="AppNavigation.filterByDate('{{ today }}')" class="text-decoration-none">
                                点击查看详情 <i class="bi bi-arrow-right"></i>
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- 逾期订单卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-warning-light me-3">
                                <i class="bi bi-exclamation-circle"></i>
                            </div>
                            <h5 class="card-title mb-0">逾期订单</h5>
                        </div>
                        <h2 class="mb-2">{{ stats.overdue_orders }}</h2>
                        <p class="card-text text-muted">
                            <a href="/overdue" class="text-decoration-none">
                                点击查看详情 <i class="bi bi-arrow-right"></i>
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- 快速搜索卡片 -->
            <div class="col-md-6 col-lg-3">
                <div class="card h-100 border-0 shadow-sm">
                    <div class="card-body">
                        <div class="d-flex align-items-center mb-3">
                            <div class="icon-box bg-success-light me-3">
                                <i class="bi bi-search"></i>
                            </div>
                            <h5 class="card-title mb-0">快速搜索</h5>
                        </div>
                        <form id="quickSearchForm" class="quick-search-form">
                            <div class="input-group mb-2">
                                <input type="text" class="form-control" id="quickSearch"
                                       placeholder="客户姓名/手机号">
                                <button class="btn btn-primary" type="submit">
                                    <i class="bi bi-search"></i>
                                </button>
                            </div>
                        </form>
                        <p class="card-text text-muted small">
                            输入客户姓名或手机号快速查询
                        </p>
                    </div>
                </div>
            </div>
        </div>

        <!-- 第二行工具卡片 -->
        <div class="row g-4 mb-4">
            <!-- 常用工具卡片 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="card-title mb-0">常用工具</h5>
                    </div>
                    <div class="card-body">
                        <div class="row g-3">
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.query.filter_data') }}" class="quick-link">
                                    <div class="quick-link-icon bg-primary-light">
                                        <i class="bi bi-calendar-date"></i>
                                    </div>
                                    <span>日期筛选</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.query.overdue_orders') }}" class="quick-link">
                                    <div class="quick-link-icon bg-warning-light">
                                        <i class="bi bi-exclamation-diamond"></i>
                                    </div>
                                    <span>逾期订单</span>
                                </a>
                            </div>
                            {% if user.has_permission('full') %}
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.summary_view') }}" class="quick-link">
                                    <div class="quick-link-icon bg-info-light">
                                        <i class="bi bi-pie-chart"></i>
                                    </div>
                                    <span>数据汇总</span>
                                </a>
                            </div>
                            {% endif %}
                            <!-- 可以根据需要添加更多快捷工具 -->
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="calculatorLink">
                                    <div class="quick-link-icon bg-success-light">
                                        <i class="bi bi-calculator"></i>
                                    </div>
                                    <span>计算器</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="calendarLink">
                                    <div class="quick-link-icon bg-secondary-light">
                                        <i class="bi bi-calendar3"></i>
                                    </div>
                                    <span>日历</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="todoListLink">
                                    <div class="quick-link-icon bg-danger-light">
                                        <i class="bi bi-check2-square"></i>
                                    </div>
                                    <span>待办事项</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.contract_generator') }}" class="quick-link">
                                    <div class="quick-link-icon bg-primary-light">
                                        <i class="bi bi-file-earmark-text"></i>
                                    </div>
                                    <span>合同生成</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="{{ url_for('main.receipt_generator') }}" class="quick-link">
                                    <div class="quick-link-icon bg-success-light">
                                        <i class="bi bi-receipt"></i>
                                    </div>
                                    <span>回执单生成</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="qrcodeGeneratorLink">
                                    <div class="quick-link-icon bg-purple-light">
                                        <i class="bi bi-qr-code"></i>
                                    </div>
                                    <span>二维码生成</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="enterpriseCreditLink">
                                    <div class="quick-link-icon bg-info-light">
                                        <i class="bi bi-building"></i>
                                    </div>
                                    <span>企业信用查询</span>
                                </a>
                            </div>
                            <div class="col-6 col-sm-4">
                                <a href="#" class="quick-link" id="serialNumberCheckLink">
                                    <div class="quick-link-icon bg-warning-light">
                                        <i class="bi bi-upc-scan"></i>
                                    </div>
                                    <span>串码查重</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 通知公告卡片 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">通知公告</h5>
                        <span class="badge bg-primary">3 条新消息</span>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush notice-list">
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="notice-icon bg-primary-light me-3">
                                    <i class="bi bi-bell"></i>
                                </div>
                                <div class="notice-content">
                                    <h6 class="mb-1">系统更新通知</h6>
                                    <p class="mb-1 text-muted small">系统将于今晚22:00-23:00进行例行维护更新</p>
                                    <span class="text-muted smaller">2023-09-10 15:30</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="notice-icon bg-success-light me-3">
                                    <i class="bi bi-check-circle"></i>
                                </div>
                                <div class="notice-content">
                                    <h6 class="mb-1">新功能上线</h6>
                                    <p class="mb-1 text-muted small">数据汇总功能已上线，欢迎使用体验反馈</p>
                                    <span class="text-muted smaller">2023-09-08 09:15</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="notice-icon bg-warning-light me-3">
                                    <i class="bi bi-exclamation-triangle"></i>
                                </div>
                                <div class="notice-content">
                                    <h6 class="mb-1">操作提醒</h6>
                                    <p class="mb-1 text-muted small">请注意及时处理逾期订单，以免影响业务进度</p>
                                    <span class="text-muted smaller">2023-09-05 16:45</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer bg-white py-2 text-center">
                        <a href="#" class="text-decoration-none">查看全部通知</a>
                    </div>
                </div>
            </div>
        </div>

        <!-- 小组件区域 -->
        <div class="row g-4">
            <!-- 日历小组件 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3">
                        <h5 class="card-title mb-0">日程安排</h5>
                    </div>
                    <div class="card-body">
                        <div id="miniCalendar" class="mini-calendar"></div>
                    </div>
                </div>
            </div>

            <!-- 待办事项小组件 -->
            <div class="col-md-6">
                <div class="card border-0 shadow-sm">
                    <div class="card-header bg-white py-3 d-flex justify-content-between align-items-center">
                        <h5 class="card-title mb-0">待办事项</h5>
                        <button class="btn btn-sm btn-primary" id="addTodoBtn">
                            <i class="bi bi-plus"></i> 添加
                        </button>
                    </div>
                    <div class="card-body p-0">
                        <ul class="list-group list-group-flush todo-list" id="todoList">
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" value="" id="todo1">
                                </div>
                                <div class="todo-content">
                                    <h6 class="mb-1">跟进张三客户的订单变更</h6>
                                    <span class="text-muted smaller">截止日期: 今天</span>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-danger">紧急</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" value="" id="todo2">
                                </div>
                                <div class="todo-content">
                                    <h6 class="mb-1">处理待审核订单</h6>
                                    <span class="text-muted smaller">截止日期: 明天</span>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-warning">中等</span>
                                </div>
                            </li>
                            <li class="list-group-item d-flex align-items-center px-3 py-3">
                                <div class="form-check me-3">
                                    <input class="form-check-input" type="checkbox" value="" id="todo3">
                                </div>
                                <div class="todo-content">
                                    <h6 class="mb-1">整理本周工作总结</h6>
                                    <span class="text-muted smaller">截止日期: 周五</span>
                                </div>
                                <div class="ms-auto">
                                    <span class="badge bg-info">一般</span>
                                </div>
                            </li>
                        </ul>
                    </div>
                    <div class="card-footer bg-white py-2 text-center">
                        <a href="#" class="text-decoration-none" id="viewAllTodos">查看全部</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block styles %}
<link rel="stylesheet" href="{{ url_for('static', filename='css/pages/home.css') }}">
<style>
/* 串码查重统计信息样式 */
.stat-item {
    padding: 10px;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.stat-number {
    font-size: 1.5rem;
    font-weight: bold;
    color: #495057;
    padding: 8px;
    border-radius: 6px;
    transition: all 0.3s ease;
    display: inline-block;
    min-width: 40px;
}

.stat-label {
    font-size: 0.875rem;
    color: #6c757d;
    margin-top: 4px;
}

/* 串码查重成功提示动画 */
.alert.fade.show {
    animation: slideInDown 0.3s ease-out;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 串码查重按钮样式 */
.quick-link .quick-link-icon.bg-warning-light {
    background-color: #fff3cd !important;
    color: #856404;
}

.quick-link:hover .quick-link-icon.bg-warning-light {
    background-color: #ffeaa7 !important;
    transform: translateY(-2px);
}

/* 统一录入样式 */
#unifiedSerialNumberInput {
    font-family: 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.4;
}

/* 结果显示样式 */
.result-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
}

.result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.result-item:last-child {
    border-bottom: none;
}

.result-label {
    font-weight: 500;
    color: #495057;
}

.result-count {
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
}

.result-count.success {
    background-color: #d4edda;
    color: #155724;
}

.result-count.warning {
    background-color: #f8d7da;
    color: #721c24;
    border: 1px solid #f5c6cb;
}

.result-count.danger {
    background-color: #f8d7da;
    color: #721c24;
}

.result-count.info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 重复串码专用样式 */
.duplicate-warning {
    background: linear-gradient(135deg, #fff3cd 0%, #fef9e7 100%);
    border: 2px solid #f39c12;
    border-radius: 8px;
    padding: 15px;
    margin-top: 15px;
    box-shadow: 0 4px 12px rgba(243, 156, 18, 0.15);
    animation: duplicateAlert 0.5s ease-out;
}

@keyframes duplicateAlert {
    0% {
        transform: scale(0.95);
        opacity: 0;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

.duplicate-warning h6 {
    color: #d68910;
    font-weight: 700;
    margin-bottom: 12px;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.duplicate-warning .small {
    color: #6c5700;
}

.duplicate-item {
    background-color: #fef9e7;
    border: 1px solid #f1c40f;
    border-left: 5px solid #e67e22;
    padding: 10px 15px;
    margin: 6px 0;
    border-radius: 6px;
    transition: all 0.2s ease;
}

.duplicate-item:hover {
    background-color: #fdf6e3;
    transform: translateX(3px);
    box-shadow: 0 2px 8px rgba(230, 126, 34, 0.2);
}

.duplicate-serial {
    font-weight: 700;
    color: #e67e22;
    font-family: 'Courier New', monospace;
    font-size: 1.1em;
    text-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.duplicate-date {
    color: #8e44ad;
    font-size: 0.85em;
    font-weight: 500;
}

/* 重复统计数字样式增强 */
.result-count.warning {
    background: linear-gradient(135deg, #f8d7da 0%, #f5c6cb 100%);
    color: #721c24;
    border: 2px solid #dc3545;
    font-weight: 700;
    animation: warningPulse 2s infinite;
}

@keyframes warningPulse {
    0%, 100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
    }
    50% {
        box-shadow: 0 0 0 8px rgba(220, 53, 69, 0);
    }
}

.batch-result-summary {
    background: #f8f9fa;
    border-radius: 8px;
    padding: 15px;
    margin: 10px 0;
}

.batch-result-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.batch-result-item:last-child {
    border-bottom: none;
}

.batch-result-label {
    font-weight: 500;
    color: #495057;
}

.batch-result-count {
    font-weight: bold;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.875rem;
}

.batch-result-count.success {
    background-color: #d4edda;
    color: #155724;
}

.batch-result-count.warning {
    background-color: #fff3cd;
    color: #856404;
}

.batch-result-count.danger {
    background-color: #f8d7da;
    color: #721c24;
}

.batch-result-count.info {
    background-color: #d1ecf1;
    color: #0c5460;
}

/* 批量处理进度条样式 */
.progress {
    height: 8px;
    border-radius: 4px;
}

.progress-bar {
    transition: width 0.3s ease;
}

/* 选项卡样式优化 */
.nav-tabs .nav-link {
    border-radius: 8px 8px 0 0;
    border: 1px solid transparent;
    color: #6c757d;
}

.nav-tabs .nav-link.active {
    background-color: #fff;
    border-color: #dee2e6 #dee2e6 #fff;
    color: #495057;
    font-weight: 500;
}

.nav-tabs .nav-link:hover {
    border-color: #e9ecef #e9ecef #dee2e6;
    color: #495057;
}
</style>
{% endblock %}

{% block scripts %}
<!-- 性能优化模块 -->
<script src="{{ url_for('static', filename='js/utils/performance-optimizer.js') }}" defer></script>

<!-- 组件模块 -->
<script src="{{ url_for('static', filename='js/components/calculator.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/components/todo.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/components/qrcode.js') }}" defer></script>
<script src="{{ url_for('static', filename='js/components/enterprise-credit.js') }}" defer></script>

<!-- 串码查重组件已在base.html中全局加载，此处移除避免重复 -->

<!-- 主页面控制器 - 使用版本控制 -->
<script src="{{ static_versioned('js/pages/home.js') }}" defer></script>

<!-- 计算器模态框 -->
<div class="modal fade" id="calculatorModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-sm">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">计算器</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <div class="calculator">
                    <div class="form-group mb-3">
                        <input type="text" class="form-control text-end" id="calcDisplay" readonly>
                    </div>
                    <div class="calc-buttons">
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">C</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">±</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">%</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">÷</button></div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">7</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">8</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">9</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">×</button></div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">4</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">5</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">6</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">-</button></div>
                        </div>
                        <div class="row g-1 mb-1">
                            <div class="col-3"><button class="btn btn-light w-100">1</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">2</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">3</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">+</button></div>
                        </div>
                        <div class="row g-1">
                            <div class="col-6"><button class="btn btn-light w-100">0</button></div>
                            <div class="col-3"><button class="btn btn-light w-100">.</button></div>
                            <div class="col-3"><button class="btn btn-warning w-100">=</button></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 添加待办事项模态框 -->
<div class="modal fade" id="todoModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">添加待办事项</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="todoForm">
                    <div class="mb-3">
                        <label for="todoTitle" class="form-label">标题</label>
                        <input type="text" class="form-control" id="todoTitle" required>
                    </div>
                    <div class="mb-3">
                        <label for="todoDueDate" class="form-label">截止日期</label>
                        <input type="date" class="form-control" id="todoDueDate">
                    </div>
                    <div class="mb-3">
                        <label for="todoPriority" class="form-label">优先级</label>
                        <select class="form-select" id="todoPriority">
                            <option value="low">低</option>
                            <option value="medium">中</option>
                            <option value="high">高</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="todoDesc" class="form-label">备注</label>
                        <textarea class="form-control" id="todoDesc" rows="3"></textarea>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">取消</button>
                <button type="button" class="btn btn-primary" id="saveTodoBtn">保存</button>
            </div>
        </div>
    </div>
</div>

<!-- 二维码生成器模态框 -->
<div class="modal fade" id="qrcodeModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-qr-code me-2"></i>二维码生成器
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="qrcodeForm">
                    <div class="mb-3">
                        <label for="qrcodeContent" class="form-label">内容</label>
                        <textarea class="form-control" id="qrcodeContent" rows="4"
                                placeholder="请输入要生成二维码的内容，如网址、文本、联系方式等..."></textarea>
                        <div class="form-text">支持网址、文本、联系方式等各种内容</div>
                    </div>
                    <div class="mb-3">
                        <label for="qrcodeSize" class="form-label">尺寸</label>
                        <select class="form-select" id="qrcodeSize">
                            <option value="128">128x128</option>
                            <option value="256" selected>256x256</option>
                            <option value="512">512x512</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <button type="button" class="btn btn-primary" id="generateQrcodeBtn">
                            <i class="bi bi-gear-fill me-1"></i>生成二维码
                        </button>
                    </div>
                    <div id="qrcodeResult" class="text-center" style="display: none;">
                        <div class="mb-3">
                            <img id="qrcodeImage" src="" alt="生成的二维码" class="border rounded">
                        </div>
                        <div class="d-flex gap-2 justify-content-center">
                            <button type="button" class="btn btn-success btn-sm" id="downloadQrcodeBtn">
                                <i class="bi bi-download me-1"></i>下载PNG
                            </button>
                            <button type="button" class="btn btn-info btn-sm" id="copyQrcodeBtn">
                                <i class="bi bi-clipboard me-1"></i>复制到剪贴板
                            </button>
                        </div>
                    </div>
                    <div id="qrcodeError" class="alert alert-danger" style="display: none;"></div>
                    <div id="qrcodeLoading" class="text-center" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">生成中...</span>
                        </div>
                        <div class="mt-2">生成中，请稍候...</div>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>

<!-- 企业信用查询模态框 -->
<div class="modal fade" id="enterpriseCreditModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-xl">
        <div class="modal-content enterprise-credit-modal">
            <div class="modal-header border-0 pb-0">
                <div class="d-flex align-items-center">
                    <div class="modal-icon">
                        <i class="bi bi-building"></i>
                    </div>
                    <div class="ms-3">
                        <h4 class="modal-title mb-0">企业信用查询</h4>
                        <p class="text-muted mb-0 small">Enterprise Credit Query</p>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>

            <div class="modal-body p-4">
                <!-- 查询配置区域 -->
                <div class="query-config-section mb-4">
                    <div class="row g-4">
                        <!-- 查询输入 -->
                        <div class="col-lg-6">
                            <div class="input-card">
                                <div class="card-header">
                                    <i class="bi bi-search me-2"></i>查询信息
                                </div>
                                <div class="card-body">
                                    <div class="mb-3">
                                        <label for="enterpriseInput" class="form-label fw-semibold">企业名称或统一社会信用代码</label>
                                        <textarea
                                            id="enterpriseInput"
                                            class="form-control modern-input"
                                            rows="3"
                                            placeholder="请输入企业名称或统一社会信用代码..."
                                        ></textarea>
                                        <div class="form-text">
                                            <i class="bi bi-info-circle me-1"></i>
                                            支持企业全称、简称或18位统一社会信用代码
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- 查询设置 -->
                        <div class="col-lg-6">
                            <div class="settings-card">
                                <div class="card-header">
                                    <i class="bi bi-gear me-2"></i>查询设置
                                </div>
                                <div class="card-body">
                                    <!-- 流式传输开关 -->
                                    <div class="mb-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <label class="form-label fw-semibold mb-0">流式传输</label>
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" id="streamingToggle" checked>
                                                <label class="form-check-label" for="streamingToggle"></label>
                                            </div>
                                        </div>
                                        <small class="text-muted">开启后实时显示查询结果</small>
                                    </div>

                                    <!-- 速度设置 -->
                                    <div class="mb-3">
                                        <label for="speedSetting" class="form-label fw-semibold">显示速度</label>
                                        <div class="speed-control">
                                            <input type="range" class="form-range" id="speedSetting"
                                                   min="0.001" max="0.1" step="0.001" value="0.001">
                                            <div class="d-flex justify-content-between small text-muted">
                                                <span>极快</span>
                                                <span id="speedValue">0.001s</span>
                                                <span>较慢</span>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- 查询按钮 -->
                                    <div class="d-grid gap-2">
                                        <button type="button" class="btn btn-primary btn-modern" id="queryEnterpriseBtn">
                                            <i class="bi bi-search me-2"></i>开始查询
                                        </button>
                                        <button type="button" class="btn btn-outline-secondary btn-sm" id="clearEnterpriseBtn">
                                            <i class="bi bi-arrow-clockwise me-1"></i>清空输入
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 查询结果区域 -->
                <div class="result-section">
                    <div class="result-header">
                        <div class="d-flex justify-content-between align-items-center">
                            <div class="d-flex align-items-center">
                                <i class="bi bi-file-text me-2"></i>
                                <span class="fw-semibold">查询结果</span>
                                <div class="status-indicator ms-3">
                                    <div id="enterpriseStatusDot" class="status-dot"></div>
                                    <span id="enterpriseStatusText" class="small">就绪</span>
                                </div>
                            </div>
                            <div class="result-actions">
                                <button type="button" class="btn btn-sm btn-outline-primary me-2" id="toggleViewBtn">
                                    <i class="bi bi-table me-1"></i>表格视图
                                </button>
                                <button type="button" class="btn btn-sm btn-outline-secondary" id="clearResultBtn">
                                    <i class="bi bi-trash me-1"></i>清空
                                </button>
                            </div>
                        </div>
                    </div>

                    <!-- 结果显示区域 -->
                    <div class="result-content">
                        <!-- 文本视图 -->
                        <div id="textView" class="result-view active">
                            <div id="enterpriseOutputArea" class="output-area">
                                <div class="empty-state">
                                    <div class="empty-icon">
                                        <i class="bi bi-search"></i>
                                    </div>
                                    <h6 class="empty-title">等待查询</h6>
                                    <p class="empty-text">请输入企业信息并点击查询按钮</p>
                                </div>
                            </div>
                        </div>

                        <!-- 表格视图 -->
                        <div id="tableView" class="result-view">
                            <div class="table-responsive">
                                <table class="table table-hover modern-table" id="enterpriseTable">
                                    <thead>
                                        <tr>
                                            <th>字段</th>
                                            <th>内容</th>
                                        </tr>
                                    </thead>
                                    <tbody id="enterpriseTableBody">
                                        <tr>
                                            <td colspan="2" class="text-center text-muted py-4">
                                                <i class="bi bi-table display-6 mb-2 d-block"></i>
                                                暂无数据
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 状态栏 -->
                <div class="status-bar">
                    <div class="d-flex justify-content-between align-items-center">
                        <div class="status-info">
                            <span id="enterpriseEventCount" class="badge bg-light text-dark">查询次数: 0</span>
                        </div>
                        <div class="query-time">
                            <small class="text-muted" id="queryTime"></small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 串码查重模态框 -->
<div class="modal fade" id="serialNumberModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-upc-scan me-2"></i>串码查重
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <!-- 统一录入表单 -->
                <form id="unifiedSerialNumberForm">
                    <div class="mb-3">
                        <label for="unifiedSerialNumberInput" class="form-label">串码录入</label>
                        <textarea class="form-control" id="unifiedSerialNumberInput" rows="6"
                                placeholder="请输入串码：&#10;• 单条录入：直接输入一个15位数字串码&#10;• 批量录入：每行输入一个15位数字串码&#10;&#10;示例：&#10;354314285665078&#10;357573129006859&#10;355011470974229"></textarea>
                        <div class="form-text">
                            <i class="bi bi-info-circle me-1"></i>
                            支持单条或批量录入，系统会自动识别。每个串码必须是15位数字
                        </div>
                        <div id="unifiedSerialNumberError" class="invalid-feedback"></div>
                    </div>

                    <div class="mb-3">
                        <label for="unifiedSerialNumberNotes" class="form-label">备注（可选）</label>
                        <input type="text" class="form-control" id="unifiedSerialNumberNotes"
                               placeholder="可添加备注信息...">
                    </div>

                    <div class="d-grid gap-2">
                        <button type="button" class="btn btn-primary" id="processSerialNumberBtn" disabled>
                            <i class="bi bi-plus-circle me-1"></i>录入
                        </button>
                        <button type="button" class="btn btn-outline-secondary btn-sm" id="clearUnifiedSerialNumberBtn">
                            <i class="bi bi-arrow-clockwise me-1"></i>清空输入
                        </button>
                    </div>

                    <!-- 处理进度 -->
                    <div id="unifiedSerialNumberProgress" class="mt-3" style="display: none;">
                        <div class="d-flex justify-content-between align-items-center mb-2">
                            <span class="small text-muted">处理进度</span>
                            <span class="small text-muted" id="unifiedProgressText">0/0</span>
                        </div>
                        <div class="progress">
                            <div class="progress-bar" role="progressbar" id="unifiedProgressBar"
                                 style="width: 0%" aria-valuenow="0" aria-valuemin="0" aria-valuemax="100"></div>
                        </div>
                    </div>

                    <!-- 结果显示区域 -->
                    <div id="unifiedSerialNumberResult" class="mt-3" style="display: none;">
                        <div class="alert" id="unifiedSerialNumberAlert" role="alert">
                            <div id="unifiedSerialNumberMessage"></div>
                        </div>
                    </div>

                    <!-- 加载状态 -->
                    <div id="unifiedSerialNumberLoading" class="text-center mt-3" style="display: none;">
                        <div class="spinner-border text-primary" role="status">
                            <span class="visually-hidden">处理中...</span>
                        </div>
                        <div class="mt-2">正在处理，请稍候...</div>
                    </div>
                </form>

                <!-- 统计信息 -->
                <div class="mt-4 pt-3 border-top">
                    <h6 class="mb-3">
                        <i class="bi bi-bar-chart me-1"></i>统计信息
                    </h6>
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number" id="totalSerialCount">-</div>
                                <div class="stat-label">总数</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number" id="todaySerialCount">-</div>
                                <div class="stat-label">今日</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <div class="stat-number" id="weekSerialCount">-</div>
                                <div class="stat-label">本周</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}