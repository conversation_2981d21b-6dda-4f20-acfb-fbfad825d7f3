/**
 * 串码查重功能模块
 * 提供串码验证、查重和录入功能
 */

class SerialNumberChecker {
    constructor() {
        // 单个录入元素
        this.modal = null;
        this.form = null;
        this.input = null;
        this.notesInput = null;
        this.checkBtn = null;
        this.clearBtn = null;
        this.resultDiv = null;
        this.alertDiv = null;
        this.messageDiv = null;
        this.loadingDiv = null;

        // 批量录入元素
        this.batchForm = null;
        this.batchInput = null;
        this.batchNotesInput = null;
        this.batchProcessBtn = null;
        this.batchClearBtn = null;
        this.batchResultDiv = null;
        this.batchAlertDiv = null;
        this.batchMessageDiv = null;
        this.batchLoadingDiv = null;
        this.batchProgressDiv = null;
        this.batchProgressBar = null;
        this.batchProgressText = null;

        // 统计信息元素
        this.totalCountEl = null;
        this.todayCountEl = null;
        this.weekCountEl = null;

        // 批量处理状态
        this.batchCheckResults = null;

        this.init();
    }
    
    init() {
        // 获取单个录入DOM元素
        this.modal = document.getElementById('serialNumberModal');
        this.form = document.getElementById('serialNumberForm');
        this.input = document.getElementById('serialNumberInput');
        this.notesInput = document.getElementById('serialNumberNotes');
        this.checkBtn = document.getElementById('checkSerialNumberBtn');
        this.clearBtn = document.getElementById('clearSerialNumberBtn');
        this.resultDiv = document.getElementById('serialNumberResult');
        this.alertDiv = document.getElementById('serialNumberAlert');
        this.messageDiv = document.getElementById('serialNumberMessage');
        this.loadingDiv = document.getElementById('serialNumberLoading');

        // 获取批量录入DOM元素
        this.batchForm = document.getElementById('batchSerialNumberForm');
        this.batchInput = document.getElementById('batchSerialNumberInput');
        this.batchNotesInput = document.getElementById('batchSerialNumberNotes');
        this.batchProcessBtn = document.getElementById('batchProcessSerialNumberBtn');
        this.batchClearBtn = document.getElementById('clearBatchSerialNumberBtn');
        this.batchResultDiv = document.getElementById('batchSerialNumberResult');
        this.batchAlertDiv = document.getElementById('batchSerialNumberAlert');
        this.batchMessageDiv = document.getElementById('batchSerialNumberMessage');
        this.batchLoadingDiv = document.getElementById('batchSerialNumberLoading');
        this.batchProgressDiv = document.getElementById('batchProgress');
        this.batchProgressBar = document.getElementById('batchProgressBar');
        this.batchProgressText = document.getElementById('batchProgressText');

        // 统计信息元素
        this.totalCountEl = document.getElementById('totalSerialCount');
        this.todayCountEl = document.getElementById('todaySerialCount');
        this.weekCountEl = document.getElementById('weekSerialCount');

        if (!this.modal) {
            console.warn('串码查重模态框未找到');
            return;
        }

        this.bindEvents();
        console.log('串码查重功能已初始化');
    }
    
    bindEvents() {
        // 绑定按钮点击事件
        if (this.checkBtn) {
            this.checkBtn.addEventListener('click', () => this.handleCheck());
        }
        
        if (this.clearBtn) {
            this.clearBtn.addEventListener('click', () => this.clearForm());
        }
        
        // 绑定输入框事件
        if (this.input) {
            this.input.addEventListener('input', () => this.validateInput());
            this.input.addEventListener('paste', (e) => this.handlePaste(e));
        }
        
        // 绑定模态框显示事件
        if (this.modal) {
            this.modal.addEventListener('shown.bs.modal', () => this.onModalShown());
            this.modal.addEventListener('hidden.bs.modal', () => this.onModalHidden());
        }
        
        // 绑定表单提交事件
        if (this.form) {
            this.form.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleCheck();
            });
        }

        // 绑定批量录入按钮事件
        if (this.batchProcessBtn) {
            this.batchProcessBtn.addEventListener('click', () => this.handleBatchProcess());
        }

        if (this.batchClearBtn) {
            this.batchClearBtn.addEventListener('click', () => this.clearBatchForm());
        }

        // 绑定批量输入框事件
        if (this.batchInput) {
            this.batchInput.addEventListener('input', () => this.validateBatchInput());
        }

        // 绑定批量表单提交事件
        if (this.batchForm) {
            this.batchForm.addEventListener('submit', (e) => {
                e.preventDefault();
                this.handleBatchProcess();
            });
        }
    }
    
    validateInput() {
        if (!this.input) return false;
        
        const value = this.input.value.trim();
        const cleaned = value.replace(/\D/g, ''); // 只保留数字
        
        // 清除之前的错误状态
        this.input.classList.remove('is-invalid');
        
        if (value && cleaned.length !== 15) {
            this.input.classList.add('is-invalid');
            const errorDiv = document.getElementById('serialNumberError');
            if (errorDiv) {
                errorDiv.textContent = `串码必须为15位数字，当前为${cleaned.length}位`;
            }
            return false;
        }
        
        return true;
    }
    
    handlePaste(event) {
        // 处理粘贴事件，自动清理非数字字符
        setTimeout(() => {
            if (this.input) {
                const value = this.input.value;
                const cleaned = value.replace(/\D/g, '');
                if (cleaned !== value) {
                    this.input.value = cleaned;
                    this.validateInput();
                }
            }
        }, 10);
    }
    
    async handleCheck() {
        if (!this.validateInput()) {
            return;
        }
        
        const serialNumber = this.input.value.trim().replace(/\D/g, '');
        const notes = this.notesInput ? this.notesInput.value.trim() : '';
        
        if (!serialNumber) {
            this.showError('请输入串码');
            return;
        }
        
        if (serialNumber.length !== 15) {
            this.showError('串码必须为15位数字');
            return;
        }
        
        this.setLoading(true);
        this.hideResult();
        
        try {
            // 先检查是否重复
            const checkResponse = await fetch('/api/serial-number/check', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    serial_number: serialNumber
                })
            });
            
            const checkResult = await checkResponse.json();
            
            if (checkResult.success) {
                // 不重复，可以添加
                const addResponse = await fetch('/api/serial-number/add', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        serial_number: serialNumber,
                        notes: notes
                    })
                });
                
                const addResult = await addResponse.json();
                
                if (addResult.success) {
                    // 格式化时间戳
                    const now = new Date();
                    const timestamp = now.toLocaleString('zh-CN', {
                        year: 'numeric',
                        month: '2-digit',
                        day: '2-digit',
                        hour: '2-digit',
                        minute: '2-digit',
                        second: '2-digit'
                    });

                    // 显示详细的成功信息
                    this.showSuccess(
                        '录入成功！',
                        `串码 ${serialNumber} 已于 ${timestamp} 成功录入系统`
                    );

                    // 清空表单（但不隐藏成功提示）
                    this.clearForm(false);

                    // 更新统计信息
                    this.updateStatistics();

                    // 3秒后自动隐藏成功提示
                    setTimeout(() => {
                        this.hideResult();
                    }, 3000);
                } else {
                    this.showError(addResult.error || '录入失败');
                }
            } else {
                // 重复
                this.showError('已重复', `串码 ${serialNumber} 已存在于系统中`);
            }
            
        } catch (error) {
            console.error('串码查重请求失败:', error);
            this.showError('网络错误', '请检查网络连接后重试');
        } finally {
            this.setLoading(false);
        }
    }
    
    clearForm(hideResult = true) {
        if (this.input) {
            this.input.value = '';
            this.input.classList.remove('is-invalid');
        }
        if (this.notesInput) {
            this.notesInput.value = '';
        }

        // 只有在指定时才隐藏结果（成功录入时我们不想立即隐藏成功提示）
        if (hideResult) {
            this.hideResult();
        }
    }
    
    setLoading(loading) {
        if (this.loadingDiv) {
            this.loadingDiv.style.display = loading ? 'block' : 'none';
        }
        if (this.checkBtn) {
            this.checkBtn.disabled = loading;
            if (loading) {
                this.checkBtn.innerHTML = '<i class="bi bi-hourglass-split me-1"></i>处理中...';
            } else {
                this.checkBtn.innerHTML = '<i class="bi bi-search me-1"></i>检查并录入';
            }
        }
    }
    
    showResult(type, title, message) {
        if (!this.resultDiv || !this.alertDiv || !this.messageDiv) return;

        // 设置alert样式，添加淡入动画
        this.alertDiv.className = `alert alert-${type} alert-dismissible fade show`;

        // 根据类型选择图标
        let icon = '';
        if (type === 'success') {
            icon = '<i class="bi bi-check-circle-fill me-2"></i>';
        } else if (type === 'danger') {
            icon = '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
        } else if (type === 'warning') {
            icon = '<i class="bi bi-exclamation-circle-fill me-2"></i>';
        }

        // 设置消息内容
        let content = '';
        if (title && message) {
            content = `${icon}<strong>${title}</strong><br><span class="ms-4">${message}</span>`;
        } else if (title) {
            content = `${icon}<strong>${title}</strong>`;
        } else {
            content = `${icon}${message || ''}`;
        }

        this.messageDiv.innerHTML = content;
        this.resultDiv.style.display = 'block';

        // 添加滚动到结果区域的效果
        setTimeout(() => {
            this.resultDiv.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 100);
    }

    showSuccess(title, message) {
        this.showResult('success', title, message);
    }

    showError(title, message) {
        this.showResult('danger', title, message);
    }
    
    hideResult() {
        if (this.resultDiv) {
            this.resultDiv.style.display = 'none';
        }
    }
    
    async updateStatistics() {
        try {
            const response = await fetch('/api/serial-number/statistics');
            const result = await response.json();

            if (result.success && result.data) {
                const stats = result.data;

                // 更新统计数字并添加高亮动画
                this.updateStatNumber(this.totalCountEl, stats.total_count || 0);
                this.updateStatNumber(this.todayCountEl, stats.today_count || 0);
                this.updateStatNumber(this.weekCountEl, stats.week_count || 0);
            }
        } catch (error) {
            console.error('获取统计信息失败:', error);
        }
    }

    updateStatNumber(element, newValue) {
        if (!element) return;

        const oldValue = parseInt(element.textContent) || 0;
        element.textContent = newValue;

        // 如果数值增加了，添加高亮动画
        if (newValue > oldValue) {
            element.style.transition = 'all 0.3s ease';
            element.style.backgroundColor = '#d4edda';
            element.style.color = '#155724';
            element.style.transform = 'scale(1.1)';

            setTimeout(() => {
                element.style.backgroundColor = '';
                element.style.color = '';
                element.style.transform = '';
            }, 1000);
        }
    }
    
    onModalShown() {
        // 模态框显示时的处理
        if (this.input) {
            this.input.focus();
        }
        this.updateStatistics();
    }
    
    onModalHidden() {
        // 模态框隐藏时的处理
        this.clearForm();
    }
    
    // 批量输入验证
    validateBatchInput() {
        if (!this.batchInput) return false;

        const text = this.batchInput.value.trim();
        const lines = text.split('\n').filter(line => line.trim());

        // 更新按钮状态
        if (this.batchProcessBtn) {
            this.batchProcessBtn.disabled = lines.length === 0;
        }

        return lines.length > 0;
    }

    // 解析批量输入
    parseBatchInput() {
        if (!this.batchInput) return [];

        const text = this.batchInput.value.trim();
        const lines = text.split('\n')
            .map(line => line.trim())
            .filter(line => line.length > 0);

        return lines;
    }

    // 批量处理（检测并录入）
    async handleBatchProcess() {
        const serialNumbers = this.parseBatchInput();

        if (serialNumbers.length === 0) {
            this.showBatchError('请输入至少一个串码');
            return;
        }

        if (serialNumbers.length > 1000) {
            this.showBatchError('一次最多只能处理1000个串码');
            return;
        }

        const notes = this.batchNotesInput ? this.batchNotesInput.value.trim() : '';

        this.showBatchLoading(true);
        this.hideBatchResult();
        this.showBatchProgress(0, serialNumbers.length);

        try {
            const response = await fetch('/api/serial-number/batch-process', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    serial_numbers: serialNumbers,
                    notes: notes
                })
            });

            const result = await response.json();

            if (result.success) {
                this.displayBatchProcessResults(result.results);

                // 更新统计信息
                this.updateStatistics();

                // 清空表单
                this.clearBatchForm(false);

                // 5秒后自动隐藏结果
                setTimeout(() => {
                    this.hideBatchResult();
                }, 5000);
            } else {
                this.showBatchError(result.error || '批量处理失败');
            }
        } catch (error) {
            console.error('批量处理错误:', error);
            this.showBatchError('网络错误，请稍后重试');
        } finally {
            this.showBatchLoading(false);
            this.hideBatchProgress();
        }
    }

    // 显示批量处理结果
    displayBatchProcessResults(results) {
        const summary = this.createBatchResultSummary(results, '批量录入结果');

        let content = summary;

        // 显示成功录入的时间戳
        if (results.success.length > 0) {
            const timestamp = new Date().toLocaleString('zh-CN');
            content += `<div class="mt-2 small text-success">录入时间: ${timestamp}</div>`;
        }

        // 显示重复详情
        if (results.duplicates.length > 0) {
            content += '<div class="mt-3"><h6 class="text-warning">重复串码:</h6>';
            content += '<div class="small text-muted">';
            results.duplicates.slice(0, 10).forEach(item => {
                const record = item.existing_record;
                const dateStr = record ? new Date(record.created_at).toLocaleString('zh-CN') : '未知时间';
                content += `<div>• ${item.serial_number} (已于${dateStr}录入)</div>`;
            });
            if (results.duplicates.length > 10) {
                content += `<div>... 还有${results.duplicates.length - 10}个重复串码</div>`;
            }
            content += '</div></div>';
        }

        // 显示无效详情
        if (results.invalid.length > 0) {
            content += '<div class="mt-3"><h6 class="text-danger">无效串码:</h6>';
            content += '<div class="small text-muted">';
            results.invalid.slice(0, 10).forEach(item => {
                content += `<div>• ${item.serial_number}: ${item.error}</div>`;
            });
            if (results.invalid.length > 10) {
                content += `<div>... 还有${results.invalid.length - 10}个无效串码</div>`;
            }
            content += '</div></div>';
        }

        // 显示失败详情
        if (results.failed.length > 0) {
            content += '<div class="mt-3"><h6 class="text-danger">录入失败:</h6>';
            content += '<div class="small text-muted">';
            results.failed.slice(0, 5).forEach(item => {
                content += `<div>• ${item.serial_number}: ${item.error}</div>`;
            });
            if (results.failed.length > 5) {
                content += `<div>... 还有${results.failed.length - 5}个失败记录</div>`;
            }
            content += '</div></div>';
        }

        this.showBatchSuccess('批量录入完成', content);
    }



    // 创建批量结果汇总
    createBatchResultSummary(results, title) {
        let content = `<div class="batch-result-summary">`;
        content += `<h6 class="mb-3">${title}</h6>`;

        // 总数
        content += `<div class="batch-result-item">`;
        content += `<span class="batch-result-label">总数:</span>`;
        content += `<span class="batch-result-count info">${results.total}</span>`;
        content += `</div>`;

        // 成功录入
        if (results.success && results.success.length > 0) {
            content += `<div class="batch-result-item">`;
            content += `<span class="batch-result-label">成功录入:</span>`;
            content += `<span class="batch-result-count success">${results.success.length}</span>`;
            content += `</div>`;
        }

        // 重复跳过
        if (results.duplicates && results.duplicates.length > 0) {
            content += `<div class="batch-result-item">`;
            content += `<span class="batch-result-label">重复跳过:</span>`;
            content += `<span class="batch-result-count warning">${results.duplicates.length}</span>`;
            content += `</div>`;
        }

        // 无效串码
        if (results.invalid && results.invalid.length > 0) {
            content += `<div class="batch-result-item">`;
            content += `<span class="batch-result-label">无效串码:</span>`;
            content += `<span class="batch-result-count danger">${results.invalid.length}</span>`;
            content += `</div>`;
        }

        // 录入失败
        if (results.failed && results.failed.length > 0) {
            content += `<div class="batch-result-item">`;
            content += `<span class="batch-result-label">录入失败:</span>`;
            content += `<span class="batch-result-count danger">${results.failed.length}</span>`;
            content += `</div>`;
        }

        content += `</div>`;
        return content;
    }

    // 批量处理辅助方法
    showBatchLoading(show) {
        if (this.batchLoadingDiv) {
            this.batchLoadingDiv.style.display = show ? 'block' : 'none';
        }

        // 禁用/启用按钮
        if (this.batchProcessBtn) this.batchProcessBtn.disabled = show;
        if (this.batchClearBtn) this.batchClearBtn.disabled = show;
    }

    showBatchProgress(current, total) {
        if (!this.batchProgressDiv || !this.batchProgressBar || !this.batchProgressText) return;

        const percentage = total > 0 ? Math.round((current / total) * 100) : 0;

        this.batchProgressDiv.style.display = 'block';
        this.batchProgressBar.style.width = `${percentage}%`;
        this.batchProgressBar.setAttribute('aria-valuenow', percentage);
        this.batchProgressText.textContent = `${current}/${total}`;
    }

    hideBatchProgress() {
        if (this.batchProgressDiv) {
            this.batchProgressDiv.style.display = 'none';
        }
    }

    showBatchResult(type, title, message) {
        if (!this.batchResultDiv || !this.batchAlertDiv || !this.batchMessageDiv) return;

        // 设置alert样式，添加淡入动画
        this.batchAlertDiv.className = `alert alert-${type} alert-dismissible fade show`;

        // 根据类型选择图标
        let icon = '';
        if (type === 'success') {
            icon = '<i class="bi bi-check-circle-fill me-2"></i>';
        } else if (type === 'danger') {
            icon = '<i class="bi bi-exclamation-triangle-fill me-2"></i>';
        } else if (type === 'warning') {
            icon = '<i class="bi bi-exclamation-circle-fill me-2"></i>';
        }

        // 设置消息内容
        let content = '';
        if (title && message) {
            content = `${icon}<strong>${title}</strong><br><div class="ms-4 mt-2">${message}</div>`;
        } else if (title) {
            content = `${icon}<strong>${title}</strong>`;
        } else {
            content = `${icon}${message || ''}`;
        }

        this.batchMessageDiv.innerHTML = content;
        this.batchResultDiv.style.display = 'block';

        // 添加滚动到结果区域的效果
        setTimeout(() => {
            this.batchResultDiv.scrollIntoView({
                behavior: 'smooth',
                block: 'nearest'
            });
        }, 100);
    }

    showBatchSuccess(title, message) {
        this.showBatchResult('success', title, message);
    }

    showBatchError(title, message) {
        this.showBatchResult('danger', title, message);
    }

    showBatchWarning(title, message) {
        this.showBatchResult('warning', title, message);
    }

    hideBatchResult() {
        if (this.batchResultDiv) {
            this.batchResultDiv.style.display = 'none';
        }
    }

    clearBatchForm(hideResult = true) {
        if (this.batchInput) {
            this.batchInput.value = '';
        }
        if (this.batchNotesInput) {
            this.batchNotesInput.value = '';
        }

        // 重置按钮状态
        if (this.batchProcessBtn) this.batchProcessBtn.disabled = true;

        // 只有在指定时才隐藏结果
        if (hideResult) {
            this.hideBatchResult();
        }

        this.hideBatchProgress();
    }

    // 公共方法：显示模态框
    show() {
        if (this.modal && window.bootstrap) {
            const modal = new bootstrap.Modal(this.modal);
            modal.show();
        }
    }
}

// 全局实例
let serialNumberChecker = null;

// 初始化函数
function initSerialNumberChecker() {
    if (!serialNumberChecker) {
        serialNumberChecker = new SerialNumberChecker();
    }
    return serialNumberChecker;
}

// 导出到全局
window.SerialNumberChecker = SerialNumberChecker;
window.initSerialNumberChecker = initSerialNumberChecker;

// DOM加载完成后自动初始化
document.addEventListener('DOMContentLoaded', function() {
    initSerialNumberChecker();
    
    // 绑定串码查重按钮点击事件
    const serialNumberLink = document.getElementById('serialNumberCheckLink');
    if (serialNumberLink) {
        serialNumberLink.addEventListener('click', function(e) {
            e.preventDefault();
            if (serialNumberChecker) {
                serialNumberChecker.show();
            }
        });
    }
});

console.log('串码查重模块已加载');
