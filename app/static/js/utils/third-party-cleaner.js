/**
 * 第三方脚本权限策略清理器
 * 防止浏览器插件和第三方脚本引起的权限策略违规
 * Third-Party Script Permissions Policy Cleaner
 */

(function() {
    'use strict';
    
    // 防止重复执行
    if (window.ThirdPartyCleanerLoaded) {
        return;
    }
    window.ThirdPartyCleanerLoaded = true;

    /**
     * 清理第三方beforeunload事件监听器
     */
    function cleanupThirdPartyUnloadListeners() {
        try {
            // 获取window上所有事件监听器的引用（浏览器内部方法）
            if (typeof window.getEventListeners === 'function') {
                const listeners = window.getEventListeners(window);
                
                if (listeners && listeners.beforeunload) {
                    listeners.beforeunload.forEach(listener => {
                        // 移除非本站点的beforeunload监听器
                        if (!listener.listener.toString().includes('visibilitychange')) {
                            window.removeEventListener('beforeunload', listener.listener, listener.useCapture);
                            console.info('🧹 已清理第三方beforeunload监听器');
                        }
                    });
                }
            }
            
            // 替代方案：重写addEventListener来拦截
            const originalAddEventListener = window.addEventListener;
            const originalRemoveEventListener = window.removeEventListener;
            
            window.addEventListener = function(type, listener, options) {
                // 拦截beforeunload和unload事件的添加
                if (type === 'beforeunload' || type === 'unload') {
                    // 检查调用栈，如果是第三方脚本，则阻止
                    const stack = new Error().stack;
                    if (stack && !stack.includes('hdsc_query_app')) {
                        console.warn('🛡️ 已阻止第三方脚本添加', type, '监听器');
                        // 不执行原始的addEventListener
                        return;
                    }
                }
                
                // 允许本站脚本和其他事件类型
                return originalAddEventListener.call(this, type, listener, options);
            };
            
            console.info('🛡️ 第三方脚本权限策略清理器已激活');
            
        } catch (error) {
            console.warn('⚠️ 清理第三方事件监听器时出错:', error);
        }
    }

    /**
     * 清理函数，在页面卸载时运行
     */
    function cleanup() {
        try {
            // 清理可能的第三方监听器
            cleanupThirdPartyUnloadListeners();
        } catch (error) {
            console.warn('⚠️ 页面清理时出错:', error);
        }
    }

    // DOM加载完成后立即清理
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', cleanupThirdPartyUnloadListeners);
    } else {
        cleanupThirdPartyUnloadListeners();
    }

    // 页面可见性变化时清理（替代beforeunload）
    document.addEventListener('visibilitychange', function() {
        if (document.visibilityState === 'hidden') {
            cleanup();
        }
    });

    // 页面完全加载后再次清理（防止延迟加载的插件）
    window.addEventListener('load', function() {
        setTimeout(cleanupThirdPartyUnloadListeners, 1000);
    });

    // 定期清理（每30秒检查一次）
    setInterval(cleanupThirdPartyUnloadListeners, 30000);

})();