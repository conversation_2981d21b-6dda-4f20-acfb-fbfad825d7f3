"""串码查重服务模块 V2

支持数据库适配器注入的新版本串码查重服务。
提供与原版本完全兼容的API接口。
"""

import logging
from typing import Dict, Any, Optional, List

from .database_adapter import DatabaseAdapter, DatabaseError
from .config_manager import config_manager
from .postgresql_adapter import PostgreSQLAdapter

# 保持向后兼容的导入
try:
    from .serial_number_service import validate_serial_number
except ImportError:
    # 如果原模块不存在，定义验证函数
    def validate_serial_number(serial_number: str) -> Dict[str, Any]:
        """验证串码格式"""
        if not serial_number:
            return {'valid': False, 'error': '串码不能为空'}
        
        cleaned = ''.join(c for c in serial_number if c.isdigit())
        
        if len(cleaned) != 15:
            return {
                'valid': False, 
                'error': f'串码必须为15位数字，当前为{len(cleaned)}位'
            }
        
        if not cleaned.isdigit():
            return {'valid': False, 'error': '串码只能包含数字'}
        
        return {'valid': True, 'cleaned_serial': cleaned}

logger = logging.getLogger(__name__)


class SerialNumberServiceV2:
    """串码查重服务类 V2
    
    支持数据库适配器注入，可以使用SQLite或PostgreSQL后端。
    """
    
    def __init__(self, db_adapter: Optional[DatabaseAdapter] = None):
        """初始化串码服务
        
        Args:
            db_adapter: 数据库适配器，如果为None则根据配置自动创建
        """
        self.db_adapter = db_adapter
        
        if self.db_adapter is None:
            self.db_adapter = self._create_default_adapter()
        
        logger.info(f"串码查重服务V2已初始化，使用适配器: {type(self.db_adapter).__name__}")
    
    def _create_default_adapter(self) -> DatabaseAdapter:
        """根据配置创建默认的数据库适配器
        
        Returns:
            DatabaseAdapter: 数据库适配器实例
        """
        try:
            db_config = config_manager.get_database_config()
            
            if db_config.database_type == 'postgresql':
                adapter = PostgreSQLAdapter(db_config)
                logger.info("使用PostgreSQL适配器")
                return adapter
            else:
                # 回退到原始的SQLite实现
                from .serial_number_service import SerialNumberService
                
                # 创建一个包装器来适配原始服务
                class SQLiteAdapterWrapper(DatabaseAdapter):
                    def __init__(self):
                        self.service = SerialNumberService()
                    
                    def connect(self) -> bool:
                        return True
                    
                    def disconnect(self) -> None:
                        pass
                    
                    def is_connected(self) -> bool:
                        return True
                    
                    def create_tables(self) -> None:
                        pass  # SQLite服务自动创建表
                    
                    def create_indexes(self) -> None:
                        pass  # SQLite服务自动创建索引
                    
                    def check_duplicate(self, serial_number: str) -> bool:
                        return self.service.check_duplicate(serial_number)
                    
                    def add_serial_number(self, serial_number: str, 
                                          created_by: Optional[str] = None, 
                                          notes: Optional[str] = None) -> Dict[str, Any]:
                        return self.service.add_serial_number(serial_number, created_by, notes)
                    
                    def get_serial_number_info(self, serial_number: str) -> Optional[Dict[str, Any]]:
                        return self.service.get_serial_number_info(serial_number)
                    
                    def get_recent_records(self, limit: int = 10) -> List[Dict[str, Any]]:
                        return self.service.get_recent_records(limit)
                    
                    def get_statistics(self) -> Dict[str, Any]:
                        return self.service.get_statistics()
                    
                    def get_record_count(self) -> int:
                        stats = self.service.get_statistics()
                        return stats.get('total_count', 0)
                    
                    def export_all_data(self) -> List[Dict[str, Any]]:
                        # 简单实现，获取所有记录
                        return self.get_recent_records(10000)  # 假设不会超过10000条
                    
                    def import_data(self, records: List[Dict[str, Any]]) -> Dict[str, Any]:
                        imported_count = 0
                        failed_count = 0
                        errors = []
                        
                        for record in records:
                            try:
                                result = self.add_serial_number(
                                    record.get('serial_number'),
                                    record.get('created_by'),
                                    record.get('notes')
                                )
                                if result.get('success'):
                                    imported_count += 1
                                else:
                                    failed_count += 1
                                    errors.append(result.get('message', 'Unknown error'))
                            except Exception as e:
                                failed_count += 1
                                errors.append(str(e))
                        
                        return {
                            'success': True,
                            'imported_count': imported_count,
                            'failed_count': failed_count,
                            'errors': errors
                        }
                    
                    def validate_schema(self) -> bool:
                        return True  # SQLite服务假设总是有效
                
                adapter = SQLiteAdapterWrapper()
                logger.info("使用SQLite适配器包装器")
                return adapter
                
        except Exception as e:
            logger.error(f"创建数据库适配器失败: {e}")
            raise
    
    def check_duplicate(self, serial_number: str) -> bool:
        """检查串码是否已存在
        
        Args:
            serial_number: 15位串码
            
        Returns:
            bool: True表示已存在（重复），False表示不存在
        """
        try:
            return self.db_adapter.check_duplicate(serial_number)
        except DatabaseError as e:
            logger.error(f"检查串码重复时发生错误: {e}")
            raise
    
    def check_serial_number(self, serial_number: str) -> Dict[str, Any]:
        """检查串码是否存在，返回详细信息
        
        Args:
            serial_number: 15位串码
            
        Returns:
            Dict: 检查结果，包含是否存在和记录信息
        """
        try:
            # 先验证格式
            validation = validate_serial_number(serial_number)
            if not validation['valid']:
                return {
                    'success': False,
                    'error': validation['error']
                }
            
            cleaned_serial = validation['cleaned_serial']
            
            # 检查是否存在
            record = self.db_adapter.get_serial_number_info(cleaned_serial)
            
            return {
                'success': True,
                'exists': record is not None,
                'record': record,
                'serial_number': cleaned_serial
            }
            
        except DatabaseError as e:
            logger.error(f"检查串码时发生错误: {e}")
            return {
                'success': False,
                'error': f'检查串码时发生错误: {str(e)}'
            }
    
    def add_serial_number(self, serial_number: str, created_by: str = None, 
                          notes: str = None) -> Dict[str, Any]:
        """添加新的串码记录
        
        Args:
            serial_number: 15位串码
            created_by: 创建者
            notes: 备注信息
            
        Returns:
            Dict: 包含操作结果的字典
        """
        try:
            return self.db_adapter.add_serial_number(serial_number, created_by, notes)
        except DatabaseError as e:
            logger.error(f"添加串码时发生错误: {e}")
            return {
                'success': False,
                'error': '系统错误',
                'message': f'添加串码时发生错误: {str(e)}'
            }
    
    def get_serial_number_info(self, serial_number: str) -> Optional[Dict[str, Any]]:
        """获取串码的详细信息
        
        Args:
            serial_number: 15位串码
            
        Returns:
            Dict: 串码信息，如果不存在则返回None
        """
        try:
            return self.db_adapter.get_serial_number_info(serial_number)
        except DatabaseError as e:
            logger.error(f"获取串码信息时发生错误: {e}")
            raise
    
    def get_recent_records(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的串码记录
        
        Args:
            limit: 返回记录数量限制
            
        Returns:
            List: 最近的串码记录列表
        """
        try:
            return self.db_adapter.get_recent_records(limit)
        except DatabaseError as e:
            logger.error(f"获取最近记录时发生错误: {e}")
            return []
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取串码统计信息
        
        Returns:
            Dict: 统计信息
        """
        try:
            return self.db_adapter.get_statistics()
        except DatabaseError as e:
            logger.error(f"获取统计信息时发生错误: {e}")
            return {
                'total_count': 0,
                'today_count': 0,
                'week_count': 0,
                'last_updated': '',
                'error': str(e)
            }
    
    def get_adapter_info(self) -> Dict[str, Any]:
        """获取当前使用的数据库适配器信息
        
        Returns:
            Dict: 适配器信息
        """
        try:
            adapter_info = self.db_adapter.get_adapter_info()
            db_config = config_manager.get_database_config()
            
            return {
                'adapter_type': adapter_info['adapter_type'],
                'database_type': db_config.database_type,
                'connection_info': db_config.get_safe_connection_string(),
                'is_connected': self.db_adapter.is_connected()
            }
        except Exception as e:
            return {
                'error': str(e)
            }


# 创建全局服务实例
# 根据配置自动选择适配器
try:
    serial_service_v2 = SerialNumberServiceV2()
except Exception as e:
    logger.error(f"初始化串码服务V2失败: {e}")
    serial_service_v2 = None


# 为了向后兼容，提供一个函数来获取适当的服务实例
def get_serial_service():
    """获取串码服务实例
    
    Returns:
        SerialNumberServiceV2: 服务实例
    """
    global serial_service_v2
    
    if serial_service_v2 is None:
        try:
            serial_service_v2 = SerialNumberServiceV2()
        except Exception as e:
            logger.error(f"创建串码服务实例失败: {e}")
            raise
    
    return serial_service_v2