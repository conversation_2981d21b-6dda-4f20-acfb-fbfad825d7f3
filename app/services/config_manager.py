"""配置管理器模块

提供应用配置的统一管理，支持环境变量、.env文件和默认配置的优先级管理。
"""

import os
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass
from dotenv import load_dotenv
from urllib.parse import urlparse

logger = logging.getLogger(__name__)


@dataclass
class DatabaseConfig:
    """数据库配置数据类"""
    database_type: str  # 'postgresql' or 'sqlite'
    connection_string: str
    timeout: int = 30
    pool_size: int = 5
    
    def validate(self) -> bool:
        """验证配置有效性
        
        Returns:
            bool: 配置是否有效
        """
        if not self.database_type or not self.connection_string:
            return False
            
        if self.database_type not in ['postgresql', 'sqlite']:
            return False
            
        if self.database_type == 'postgresql':
            return self._validate_postgresql_uri()
        elif self.database_type == 'sqlite':
            return self._validate_sqlite_path()
            
        return False
    
    def _validate_postgresql_uri(self) -> bool:
        """验证PostgreSQL连接字符串"""
        try:
            parsed = urlparse(self.connection_string)
            return (
                parsed.scheme == 'postgresql' and
                parsed.hostname and
                parsed.port and
                parsed.username and
                parsed.password and
                parsed.path
            )
        except Exception:
            return False
    
    def _validate_sqlite_path(self) -> bool:
        """验证SQLite数据库路径"""
        try:
            # 检查路径格式是否合理
            return self.connection_string.endswith('.db')
        except Exception:
            return False
    
    def get_safe_connection_string(self) -> str:
        """获取安全的连接字符串（隐藏密码）
        
        Returns:
            str: 隐藏密码的连接字符串
        """
        if self.database_type == 'postgresql':
            try:
                parsed = urlparse(self.connection_string)
                safe_uri = (f"{parsed.scheme}://{parsed.username}:***@"
                            f"{parsed.hostname}:{parsed.port}{parsed.path}")
                return safe_uri
            except Exception:
                return "postgresql://***:***@***:***/**"
        else:
            return self.connection_string


class ConfigurationError(Exception):
    """配置错误异常"""
    pass


class ConfigManager:
    """配置管理器
    
    负责加载和管理应用配置，支持多种配置源的优先级管理。
    """
    
    def __init__(self, env_file: Optional[str] = None):
        """初始化配置管理器
        
        Args:
            env_file: .env文件路径，默认为项目根目录下的.env
        """
        self._config_cache: Dict[str, Any] = {}
        self._env_file = env_file or '.env'
        self._load_environment()
        
        logger.info("配置管理器已初始化")
    
    def _load_environment(self) -> None:
        """加载环境变量"""
        try:
            # 加载.env文件（如果存在）
            if os.path.exists(self._env_file):
                load_dotenv(self._env_file)
                logger.info(f"已加载环境配置文件: {self._env_file}")
            else:
                logger.info(f"环境配置文件不存在: {self._env_file}，使用系统环境变量")
        except Exception as e:
            logger.warning(f"加载环境配置失败: {e}")
    
    def get_database_config(self) -> DatabaseConfig:
        """获取数据库配置
        
        Returns:
            DatabaseConfig: 数据库配置对象
            
        Raises:
            ConfigurationError: 配置无效时抛出
        """
        cache_key = 'database_config'
        if cache_key in self._config_cache:
            return self._config_cache[cache_key]
        
        # 获取数据库类型
        db_type = self._get_env_value('DATABASE_TYPE', 'sqlite')
        
        # 获取连接字符串
        if db_type == 'postgresql':
            default_pg_uri = (
                'postgresql://flask_user:flask_password@'
                '172.17.0.3:5432/flask_db'
            )
            connection_string = self._get_env_value(
                'DATABASE_URI', default_pg_uri
            )
        else:
            # SQLite默认路径
            project_root = os.path.dirname(os.path.dirname(
                os.path.dirname(os.path.abspath(__file__))
            ))
            default_sqlite_path = os.path.join(
                project_root, 'serial_numbers.db'
            )
            connection_string = self._get_env_value(
                'DATABASE_URI', default_sqlite_path
            )
        
        # 获取其他配置
        timeout = int(self._get_env_value('DATABASE_TIMEOUT', '30'))
        pool_size = int(self._get_env_value('DATABASE_POOL_SIZE', '5'))
        
        config = DatabaseConfig(
            database_type=db_type,
            connection_string=connection_string,
            timeout=timeout,
            pool_size=pool_size
        )
        
        # 验证配置
        if not config.validate():
            safe_conn = config.get_safe_connection_string()
            raise ConfigurationError(f"数据库配置无效: {safe_conn}")
        
        # 缓存配置
        self._config_cache[cache_key] = config
        
        safe_conn = config.get_safe_connection_string()
        logger.info(f"数据库配置已加载: {config.database_type} - {safe_conn}")
        return config
    
    def is_postgresql_enabled(self) -> bool:
        """检查是否启用PostgreSQL
        
        Returns:
            bool: 是否启用PostgreSQL
        """
        try:
            config = self.get_database_config()
            return config.database_type == 'postgresql'
        except Exception:
            return False
    
    def is_sqlite_enabled(self) -> bool:
        """检查是否启用SQLite
        
        Returns:
            bool: 是否启用SQLite
        """
        try:
            config = self.get_database_config()
            return config.database_type == 'sqlite'
        except Exception:
            return True  # 默认使用SQLite
    
    def get_api_key(self) -> str:
        """获取API密钥
        
        Returns:
            str: API密钥
        """
        return self._get_env_value('API_KEY', 'lxw8025031')
    
    def get_log_config(self) -> Dict[str, str]:
        """获取日志配置
        
        Returns:
            Dict: 日志配置
        """
        return {
            'log_file_path': self._get_env_value('LOG_FILE_PATH', 'logs/server.log'),
            'log_level': self._get_env_value('LOG_LEVEL', 'INFO')
        }
    
    def get_flask_config(self) -> Dict[str, str]:
        """获取Flask配置
        
        Returns:
            Dict: Flask配置
        """
        return {
            'flask_env': self._get_env_value('FLASK_ENV', 'development'),
            'flask_app': self._get_env_value('FLASK_APP', 'run.py'),
            'secret_key': self._get_env_value(
                'SECRET_KEY', 'dev_key_please_change_in_production'
            )
        }
    
    def validate_config(self) -> bool:
        """验证所有配置
        
        Returns:
            bool: 配置是否全部有效
        """
        try:
            # 验证数据库配置
            db_config = self.get_database_config()
            if not db_config.validate():
                logger.error("数据库配置验证失败")
                return False
            
            # 验证API密钥
            api_key = self.get_api_key()
            if not api_key or len(api_key) < 6:
                logger.error("API密钥配置无效")
                return False
            
            # 验证日志配置
            log_config = self.get_log_config()
            log_dir = os.path.dirname(log_config['log_file_path'])
            if log_dir and not os.path.exists(log_dir):
                try:
                    os.makedirs(log_dir, exist_ok=True)
                except Exception as e:
                    logger.error(f"无法创建日志目录: {e}")
                    return False
            
            logger.info("所有配置验证通过")
            return True
            
        except Exception as e:
            logger.error(f"配置验证失败: {e}")
            return False
    
    def get_backup_config(self) -> Dict[str, str]:
        """获取备份配置
        
        Returns:
            Dict: 备份配置
        """
        return {
            'sqlite_backup_path': self._get_env_value(
                'SQLITE_BACKUP_PATH', 'serial_numbers.db.backup'
            )
        }
    
    def _get_env_value(self, key: str, default: str = '') -> str:
        """获取环境变量值
        
        Args:
            key: 环境变量键名
            default: 默认值
            
        Returns:
            str: 环境变量值
        """
        return os.getenv(key, default)
    
    def reload_config(self) -> None:
        """重新加载配置"""
        self._config_cache.clear()
        self._load_environment()
        logger.info("配置已重新加载")
    
    def get_config_summary(self) -> Dict[str, Any]:
        """获取配置摘要
        
        Returns:
            Dict: 配置摘要信息
        """
        try:
            db_config = self.get_database_config()
            return {
                'database_type': db_config.database_type,
                'database_connection': db_config.get_safe_connection_string(),
                'api_key_configured': bool(self.get_api_key()),
                'flask_env': self.get_flask_config()['flask_env'],
                'log_file': self.get_log_config()['log_file_path'],
                'config_valid': self.validate_config()
            }
        except Exception as e:
            return {
                'error': str(e),
                'config_valid': False
            }


# 创建全局配置管理器实例
config_manager = ConfigManager()